package com.tqhit.battery.one.features.emoji.domain.use_case

import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for retrieving battery styles from the repository.
 * 
 * This use case follows the established stats module architecture pattern:
 * - Provides clean separation between presentation and data layers
 * - Handles business logic for filtering and categorizing styles
 * - Integrates with repository's reactive StateFlow for real-time updates
 * - Includes comprehensive error handling and logging
 * - Supports various filtering options (category, premium status, search)
 * 
 * The use case acts as a bridge between the presentation layer (ViewModel) 
 * and the data layer (Repository), ensuring proper abstraction and testability.
 */
@Singleton
class GetBatteryStylesUseCase @Inject constructor(
    private val batteryStyleRepository: BatteryStyleRepository
) {
    
    companion object {
        private const val TAG = "GetBatteryStylesUseCase"
    }
    
    /**
     * Gets all battery styles as a reactive flow.
     * This provides real-time updates when styles are refreshed from remote config.
     *
     * @return Flow of all available battery styles
     */
    fun getAllStylesFlow(): Flow<List<BatteryStyle>> {
        Log.d(TAG, "FLOW: Providing all styles flow")
        return batteryStyleRepository.batteryStylesFlow
    }

    /**
     * Gets loading state as a reactive flow.
     * Useful for showing loading indicators in the UI.
     *
     * @return Flow indicating if styles are currently being loaded
     */
    fun getLoadingStateFlow(): Flow<Boolean> {
        Log.d(TAG, "FLOW: Providing loading state flow")
        return batteryStyleRepository.isLoadingFlow
    }
    
    /**
     * Gets all battery styles with optional force refresh.
     * 
     * @param forceRefresh Whether to force refresh from remote config
     * @return List of all available battery styles
     */
    suspend fun getAllStyles(forceRefresh: Boolean = false): List<BatteryStyle> {
        Log.d(TAG, "GET_ALL: Retrieving all styles (forceRefresh: $forceRefresh)")
        
        return try {
            val styles = batteryStyleRepository.getAllStyles(forceRefresh)
            Log.d(TAG, "GET_ALL: Successfully retrieved ${styles.size} styles")
            styles
        } catch (e: Exception) {
            Log.e(TAG, "GET_ALL: Failed to retrieve styles", e)
            emptyList()
        }
    }
    
    /**
     * Gets battery styles filtered by category.
     * 
     * @param category The category to filter by
     * @param forceRefresh Whether to force refresh from remote config
     * @return List of battery styles in the specified category
     */
    suspend fun getStylesByCategory(
        category: BatteryStyleCategory,
        forceRefresh: Boolean = false
    ): List<BatteryStyle> {
        Log.d(TAG, "GET_CATEGORY: Retrieving styles for category: ${category.displayName} (forceRefresh: $forceRefresh)")
        
        return try {
            val styles = batteryStyleRepository.getStylesByCategory(category, forceRefresh)
            Log.d(TAG, "GET_CATEGORY: Successfully retrieved ${styles.size} styles for category: ${category.displayName}")
            styles
        } catch (e: Exception) {
            Log.e(TAG, "GET_CATEGORY: Failed to retrieve styles for category: ${category.displayName}", e)
            emptyList()
        }
    }
    
    /**
     * Gets popular/trending battery styles.
     * These are styles marked as popular in the remote config or local JSON.
     * 
     * @param forceRefresh Whether to force refresh from remote config
     * @return List of popular battery styles
     */
    suspend fun getPopularStyles(forceRefresh: Boolean = false): List<BatteryStyle> {
        Log.d(TAG, "GET_POPULAR: Retrieving popular styles (forceRefresh: $forceRefresh)")
        
        return try {
            val styles = batteryStyleRepository.getPopularStyles(forceRefresh)
            Log.d(TAG, "GET_POPULAR: Successfully retrieved ${styles.size} popular styles")
            styles
        } catch (e: Exception) {
            Log.e(TAG, "GET_POPULAR: Failed to retrieve popular styles", e)
            emptyList()
        }
    }
    
    /**
     * Gets premium battery styles.
     * These require unlock through purchase or ad viewing.
     * 
     * @param forceRefresh Whether to force refresh from remote config
     * @return List of premium battery styles
     */
    suspend fun getPremiumStyles(forceRefresh: Boolean = false): List<BatteryStyle> {
        Log.d(TAG, "GET_PREMIUM: Retrieving premium styles (forceRefresh: $forceRefresh)")
        
        return try {
            val styles = batteryStyleRepository.getPremiumStyles(forceRefresh)
            Log.d(TAG, "GET_PREMIUM: Successfully retrieved ${styles.size} premium styles")
            styles
        } catch (e: Exception) {
            Log.e(TAG, "GET_PREMIUM: Failed to retrieve premium styles", e)
            emptyList()
        }
    }
    
    /**
     * Gets free battery styles.
     * These are available without any unlock requirement.
     * 
     * @param forceRefresh Whether to force refresh from remote config
     * @return List of free battery styles
     */
    suspend fun getFreeStyles(forceRefresh: Boolean = false): List<BatteryStyle> {
        Log.d(TAG, "GET_FREE: Retrieving free styles (forceRefresh: $forceRefresh)")
        
        return try {
            val styles = batteryStyleRepository.getFreeStyles(forceRefresh)
            Log.d(TAG, "GET_FREE: Successfully retrieved ${styles.size} free styles")
            styles
        } catch (e: Exception) {
            Log.e(TAG, "GET_FREE: Failed to retrieve free styles", e)
            emptyList()
        }
    }
    
    /**
     * Searches battery styles by query string.
     * Searches in style name and category display name.
     * 
     * @param query Search query (case-insensitive)
     * @param forceRefresh Whether to force refresh from remote config
     * @return List of battery styles matching the search query
     */
    suspend fun searchStyles(
        query: String,
        forceRefresh: Boolean = false
    ): List<BatteryStyle> {
        Log.d(TAG, "SEARCH: Searching styles with query: '$query' (forceRefresh: $forceRefresh)")
        
        return try {
            val styles = batteryStyleRepository.searchStyles(query, forceRefresh)
            Log.d(TAG, "SEARCH: Successfully found ${styles.size} styles matching query: '$query'")
            styles
        } catch (e: Exception) {
            Log.e(TAG, "SEARCH: Failed to search styles with query: '$query'", e)
            emptyList()
        }
    }
    
    /**
     * Gets a specific battery style by ID.
     * 
     * @param styleId The unique identifier of the style
     * @return The battery style if found, null otherwise
     */
    suspend fun getStyleById(styleId: String): BatteryStyle? {
        Log.d(TAG, "GET_BY_ID: Retrieving style with ID: $styleId")
        
        return try {
            val style = batteryStyleRepository.getStyleById(styleId)
            if (style != null) {
                Log.d(TAG, "GET_BY_ID: Successfully retrieved style: ${style.name}")
            } else {
                Log.w(TAG, "GET_BY_ID: Style not found with ID: $styleId")
            }
            style
        } catch (e: Exception) {
            Log.e(TAG, "GET_BY_ID: Failed to retrieve style with ID: $styleId", e)
            null
        }
    }
    
    /**
     * Forces a refresh of battery styles from remote config.
     * Useful for pull-to-refresh functionality.
     * 
     * @return True if refresh was successful, false otherwise
     */
    suspend fun refreshStyles(): Boolean {
        Log.d(TAG, "REFRESH: Forcing styles refresh")
        
        return try {
            val success = batteryStyleRepository.refreshStyles()
            if (success) {
                Log.d(TAG, "REFRESH: Successfully refreshed styles")
            } else {
                Log.w(TAG, "REFRESH: Failed to refresh styles")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "REFRESH: Exception during styles refresh", e)
            false
        }
    }
    
    /**
     * Gets currently cached styles without triggering a network request.
     * Useful for immediate UI updates.
     * 
     * @return List of currently cached battery styles
     */
    fun getCurrentStyles(): List<BatteryStyle> {
        Log.d(TAG, "GET_CURRENT: Retrieving current cached styles")
        
        return try {
            val styles = batteryStyleRepository.getCurrentStyles()
            Log.d(TAG, "GET_CURRENT: Retrieved ${styles.size} cached styles")
            styles
        } catch (e: Exception) {
            Log.e(TAG, "GET_CURRENT: Failed to retrieve cached styles", e)
            emptyList()
        }
    }
    
    /**
     * Checks if there is cached data available.
     * Useful for determining if we need to show loading states.
     * 
     * @return True if cached data is available, false otherwise
     */
    fun hasCachedData(): Boolean {
        return try {
            val hasData = batteryStyleRepository.hasCachedData()
            Log.d(TAG, "HAS_CACHED: Cached data available: $hasData")
            hasData
        } catch (e: Exception) {
            Log.e(TAG, "HAS_CACHED: Failed to check cached data", e)
            false
        }
    }
}
