package com.tqhit.battery.one.features.emoji.presentation.gallery

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory

/**
 * Events for the Battery Gallery screen following MVI pattern.
 * 
 * This event class follows the established stats module architecture pattern:
 * - Sealed class hierarchy for type-safe event handling
 * - Clear separation of user actions and system events
 * - Comprehensive coverage of all possible user interactions
 * - Integration with CoreBatteryStatsService for feature state changes
 * - Support for navigation and customization flows
 * 
 * Events are categorized into:
 * - Data events (loading, refreshing, searching)
 * - Filter events (category, premium, search)
 * - UI events (selection, navigation, toggles)
 * - Feature events (enable/disable, permissions)
 * - Error events (retry, dismiss)
 */
sealed class BatteryGalleryEvent {
    
    // === Data Loading Events ===
    
    /**
     * Event to load initial battery styles data.
     * Triggered when the screen is first opened.
     */
    object LoadInitialData : BatteryGalleryEvent()
    
    /**
     * Event to refresh battery styles from remote config.
     * Triggered by pull-to-refresh or manual refresh button.
     */
    object RefreshData : BatteryGalleryEvent()
    
    /**
     * Event to retry loading data after an error.
     * Triggered by retry button in error states.
     */
    object RetryLoading : BatteryGalleryEvent()
    
    // === Filter Events ===
    
    /**
     * Event to filter styles by category.
     * 
     * @param category The category to filter by, null to show all
     */
    data class FilterByCategory(val category: BatteryStyleCategory?) : BatteryGalleryEvent()
    
    /**
     * Event to toggle showing only free styles.
     * 
     * @param showOnlyFree Whether to show only free styles
     */
    data class ToggleShowOnlyFree(val showOnlyFree: Boolean) : BatteryGalleryEvent()
    
    /**
     * Event to toggle showing only premium styles.
     * 
     * @param showOnlyPremium Whether to show only premium styles
     */
    data class ToggleShowOnlyPremium(val showOnlyPremium: Boolean) : BatteryGalleryEvent()
    
    /**
     * Event to toggle showing only popular styles.
     * 
     * @param showOnlyPopular Whether to show only popular styles
     */
    data class ToggleShowOnlyPopular(val showOnlyPopular: Boolean) : BatteryGalleryEvent()
    
    /**
     * Event to search styles by query.
     * 
     * @param query Search query string
     */
    data class SearchStyles(val query: String) : BatteryGalleryEvent()
    
    /**
     * Event to clear all active filters.
     * Resets the gallery to show all styles.
     */
    object ClearAllFilters : BatteryGalleryEvent()
    
    // === UI Interaction Events ===
    
    /**
     * Event when a battery style is selected.
     * This should navigate to the customization screen.
     * 
     * @param style The selected battery style
     */
    data class SelectStyle(val style: BatteryStyle) : BatteryGalleryEvent()
    
    /**
     * Event to toggle the category filter UI visibility.
     * 
     * @param show Whether to show the category filter
     */
    data class ToggleCategoryFilter(val show: Boolean) : BatteryGalleryEvent()
    
    /**
     * Event to toggle the search bar visibility.
     * 
     * @param show Whether to show the search bar
     */
    data class ToggleSearchBar(val show: Boolean) : BatteryGalleryEvent()
    
    /**
     * Event to show style preview.
     * This might show a preview dialog or expand the item.
     * 
     * @param style The style to preview
     */
    data class PreviewStyle(val style: BatteryStyle) : BatteryGalleryEvent()
    
    // === Feature Toggle Events ===
    
    /**
     * Event to toggle the emoji battery feature on/off.
     * This is the main feature toggle at the top of the screen.
     * 
     * @param enabled Whether the emoji battery feature should be enabled
     */
    data class ToggleEmojiBatteryFeature(val enabled: Boolean) : BatteryGalleryEvent()
    
    /**
     * Event to request necessary permissions for the emoji battery feature.
     * This includes accessibility service and overlay permissions.
     */
    object RequestPermissions : BatteryGalleryEvent()
    
    /**
     * Event triggered when permissions are granted.
     * Updates the UI to reflect the new permission state.
     */
    object PermissionsGranted : BatteryGalleryEvent()
    
    /**
     * Event triggered when permissions are denied.
     * Shows appropriate error messages or guidance.
     */
    object PermissionsDenied : BatteryGalleryEvent()
    
    // === Premium/Monetization Events ===
    
    /**
     * Event to unlock a premium style.
     * This might trigger an ad view or purchase flow.
     * 
     * @param style The premium style to unlock
     */
    data class UnlockPremiumStyle(val style: BatteryStyle) : BatteryGalleryEvent()
    
    /**
     * Event to watch an ad to unlock premium content.
     * 
     * @param style The style to unlock via ad
     */
    data class WatchAdToUnlock(val style: BatteryStyle) : BatteryGalleryEvent()
    
    /**
     * Event to purchase premium access.
     * This might unlock all premium styles or a specific pack.
     */
    object PurchasePremiumAccess : BatteryGalleryEvent()
    
    /**
     * Event triggered when premium content is successfully unlocked.
     * 
     * @param styleId The ID of the unlocked style, or null if all premium unlocked
     */
    data class PremiumUnlocked(val styleId: String?) : BatteryGalleryEvent()
    
    // === Error Handling Events ===
    
    /**
     * Event to dismiss error messages.
     * Clears the error state from the UI.
     */
    object DismissError : BatteryGalleryEvent()
    
    /**
     * Event triggered when an image fails to load.
     * This helps track and handle missing emoji assets.
     * 
     * @param styleId The ID of the style with failed image
     * @param imageType The type of image that failed (battery or emoji)
     */
    data class ImageLoadFailed(val styleId: String, val imageType: String) : BatteryGalleryEvent()
    
    // === Navigation Events ===
    
    /**
     * Event to navigate to the customization screen.
     * 
     * @param style The style to customize
     */
    data class NavigateToCustomization(val style: BatteryStyle) : BatteryGalleryEvent()
    
    /**
     * Event to navigate to settings.
     * Might be triggered from permission requests or feature toggles.
     */
    object NavigateToSettings : BatteryGalleryEvent()
    
    /**
     * Event to show information about the emoji battery feature.
     * This might show a help dialog or tutorial.
     */
    object ShowFeatureInfo : BatteryGalleryEvent()
    
    // === System Events ===
    
    /**
     * Event triggered when the app comes back to foreground.
     * Used to refresh data or check permission states.
     */
    object OnResume : BatteryGalleryEvent()
    
    /**
     * Event triggered when the app goes to background.
     * Used for cleanup or state saving.
     */
    object OnPause : BatteryGalleryEvent()
    
    /**
     * Event triggered when battery state changes.
     * This comes from CoreBatteryStatsService integration.
     * 
     * @param isCharging Current charging state
     * @param batteryLevel Current battery percentage
     */
    data class BatteryStateChanged(val isCharging: Boolean, val batteryLevel: Int) : BatteryGalleryEvent()
    
    companion object {
        /**
         * Helper function to create a search event with debouncing consideration.
         * The ViewModel should implement debouncing for search queries.
         * 
         * @param query The search query
         * @return SearchStyles event
         */
        fun createSearchEvent(query: String): SearchStyles {
            return SearchStyles(query.trim())
        }
        
        /**
         * Helper function to create a category filter event.
         * 
         * @param category The category to filter by
         * @return FilterByCategory event
         */
        fun createCategoryFilterEvent(category: BatteryStyleCategory?): FilterByCategory {
            return FilterByCategory(category)
        }
    }
}
