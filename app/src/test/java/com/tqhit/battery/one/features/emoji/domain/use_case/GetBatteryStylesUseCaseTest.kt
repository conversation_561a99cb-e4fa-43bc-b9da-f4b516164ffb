package com.tqhit.battery.one.features.emoji.domain.use_case

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for GetBatteryStylesUseCase.
 * 
 * Tests the use case layer following the established stats module testing patterns:
 * - Uses MockK for mocking dependencies
 * - Tests all public methods and error scenarios
 * - Verifies proper repository interaction
 * - Tests reactive flows and caching behavior
 * - Includes comprehensive error handling tests
 */
class GetBatteryStylesUseCaseTest {
    
    private lateinit var mockRepository: BatteryStyleRepository
    private lateinit var useCase: GetBatteryStylesUseCase
    
    private val testStyles = listOf(
        BatteryStyle(
            id = "test1",
            name = "Test Style 1",
            category = BatteryStyleCategory.HEART,
            batteryImageUrl = "https://example.com/battery1.png",
            emojiImageUrl = "https://example.com/emoji1.png",
            isPremium = false,
            isPopular = true
        ),
        BatteryStyle(
            id = "test2",
            name = "Test Style 2",
            category = BatteryStyleCategory.CUTE,
            batteryImageUrl = "https://example.com/battery2.png",
            emojiImageUrl = "https://example.com/emoji2.png",
            isPremium = true,
            isPopular = false
        ),
        BatteryStyle(
            id = "test3",
            name = "Test Style 3",
            category = BatteryStyleCategory.HEART,
            batteryImageUrl = "https://example.com/battery3.png",
            emojiImageUrl = "https://example.com/emoji3.png",
            isPremium = false,
            isPopular = true
        )
    )
    
    @Before
    fun setUp() {
        mockRepository = mockk()
        useCase = GetBatteryStylesUseCase(mockRepository)
    }
    
    @Test
    fun `getAllStylesFlow returns repository flow`() {
        // Given
        val testFlow = MutableStateFlow(testStyles)
        every { mockRepository.batteryStylesFlow } returns testFlow
        
        // When
        val result = useCase.getAllStylesFlow()
        
        // Then
        assertEquals(testFlow, result)
    }
    
    @Test
    fun `getLoadingStateFlow returns repository loading flow`() {
        // Given
        val testFlow = MutableStateFlow(false)
        every { mockRepository.isLoadingFlow } returns testFlow
        
        // When
        val result = useCase.getLoadingStateFlow()
        
        // Then
        assertEquals(testFlow, result)
    }
    
    @Test
    fun `getAllStyles returns styles from repository`() = runTest {
        // Given
        coEvery { mockRepository.getAllStyles(false) } returns testStyles
        
        // When
        val result = useCase.getAllStyles(false)
        
        // Then
        assertEquals(testStyles, result)
        coVerify { mockRepository.getAllStyles(false) }
    }
    
    @Test
    fun `getAllStyles with force refresh calls repository with correct parameter`() = runTest {
        // Given
        coEvery { mockRepository.getAllStyles(true) } returns testStyles
        
        // When
        val result = useCase.getAllStyles(true)
        
        // Then
        assertEquals(testStyles, result)
        coVerify { mockRepository.getAllStyles(true) }
    }
    
    @Test
    fun `getAllStyles returns empty list on exception`() = runTest {
        // Given
        coEvery { mockRepository.getAllStyles(any()) } throws RuntimeException("Test error")
        
        // When
        val result = useCase.getAllStyles()
        
        // Then
        assertTrue(result.isEmpty())
    }
    
    @Test
    fun `getStylesByCategory returns filtered styles`() = runTest {
        // Given
        val heartStyles = testStyles.filter { it.category == BatteryStyleCategory.HEART }
        coEvery { mockRepository.getStylesByCategory(BatteryStyleCategory.HEART, false) } returns heartStyles
        
        // When
        val result = useCase.getStylesByCategory(BatteryStyleCategory.HEART)
        
        // Then
        assertEquals(heartStyles, result)
        coVerify { mockRepository.getStylesByCategory(BatteryStyleCategory.HEART, false) }
    }
    
    @Test
    fun `getStylesByCategory returns empty list on exception`() = runTest {
        // Given
        coEvery { mockRepository.getStylesByCategory(any(), any()) } throws RuntimeException("Test error")
        
        // When
        val result = useCase.getStylesByCategory(BatteryStyleCategory.HEART)
        
        // Then
        assertTrue(result.isEmpty())
    }
    
    @Test
    fun `getPopularStyles returns popular styles`() = runTest {
        // Given
        val popularStyles = testStyles.filter { it.isPopular }
        coEvery { mockRepository.getPopularStyles(false) } returns popularStyles
        
        // When
        val result = useCase.getPopularStyles()
        
        // Then
        assertEquals(popularStyles, result)
        coVerify { mockRepository.getPopularStyles(false) }
    }
    
    @Test
    fun `getPremiumStyles returns premium styles`() = runTest {
        // Given
        val premiumStyles = testStyles.filter { it.isPremium }
        coEvery { mockRepository.getPremiumStyles(false) } returns premiumStyles
        
        // When
        val result = useCase.getPremiumStyles()
        
        // Then
        assertEquals(premiumStyles, result)
        coVerify { mockRepository.getPremiumStyles(false) }
    }
    
    @Test
    fun `getFreeStyles returns free styles`() = runTest {
        // Given
        val freeStyles = testStyles.filter { !it.isPremium }
        coEvery { mockRepository.getFreeStyles(false) } returns freeStyles
        
        // When
        val result = useCase.getFreeStyles()
        
        // Then
        assertEquals(freeStyles, result)
        coVerify { mockRepository.getFreeStyles(false) }
    }
    
    @Test
    fun `searchStyles returns matching styles`() = runTest {
        // Given
        val query = "Test"
        val matchingStyles = testStyles.filter { it.name.contains(query) }
        coEvery { mockRepository.searchStyles(query, false) } returns matchingStyles
        
        // When
        val result = useCase.searchStyles(query)
        
        // Then
        assertEquals(matchingStyles, result)
        coVerify { mockRepository.searchStyles(query, false) }
    }
    
    @Test
    fun `getStyleById returns correct style`() = runTest {
        // Given
        val targetStyle = testStyles.first()
        coEvery { mockRepository.getStyleById(targetStyle.id) } returns targetStyle
        
        // When
        val result = useCase.getStyleById(targetStyle.id)
        
        // Then
        assertEquals(targetStyle, result)
        coVerify { mockRepository.getStyleById(targetStyle.id) }
    }
    
    @Test
    fun `getStyleById returns null when not found`() = runTest {
        // Given
        coEvery { mockRepository.getStyleById("nonexistent") } returns null
        
        // When
        val result = useCase.getStyleById("nonexistent")
        
        // Then
        assertNull(result)
    }
    
    @Test
    fun `refreshStyles returns success from repository`() = runTest {
        // Given
        coEvery { mockRepository.refreshStyles() } returns true
        
        // When
        val result = useCase.refreshStyles()
        
        // Then
        assertTrue(result)
        coVerify { mockRepository.refreshStyles() }
    }
    
    @Test
    fun `refreshStyles returns false on exception`() = runTest {
        // Given
        coEvery { mockRepository.refreshStyles() } throws RuntimeException("Test error")
        
        // When
        val result = useCase.refreshStyles()
        
        // Then
        assertFalse(result)
    }
    
    @Test
    fun `getCurrentStyles returns cached styles`() {
        // Given
        every { mockRepository.getCurrentStyles() } returns testStyles
        
        // When
        val result = useCase.getCurrentStyles()
        
        // Then
        assertEquals(testStyles, result)
    }
    
    @Test
    fun `getCurrentStyles returns empty list on exception`() {
        // Given
        every { mockRepository.getCurrentStyles() } throws RuntimeException("Test error")
        
        // When
        val result = useCase.getCurrentStyles()
        
        // Then
        assertTrue(result.isEmpty())
    }
    
    @Test
    fun `hasCachedData returns repository result`() {
        // Given
        every { mockRepository.hasCachedData() } returns true
        
        // When
        val result = useCase.hasCachedData()
        
        // Then
        assertTrue(result)
    }
    
    @Test
    fun `hasCachedData returns false on exception`() {
        // Given
        every { mockRepository.hasCachedData() } throws RuntimeException("Test error")
        
        // When
        val result = useCase.hasCachedData()
        
        // Then
        assertFalse(result)
    }
}
