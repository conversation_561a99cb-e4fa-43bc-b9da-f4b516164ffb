package com.tqhit.battery.one.features.emoji.presentation.gallery

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test

/**
 * Unit tests for BatteryGalleryViewModel.
 * 
 * Tests the ViewModel layer following the established stats module testing patterns:
 * - Uses MockK for mocking dependencies
 * - Tests MVI pattern implementation
 * - Verifies state management and event handling
 * - Tests CoreBatteryStatsProvider integration
 * - Includes comprehensive error handling tests
 */
@OptIn(ExperimentalCoroutinesApi::class)
class BatteryGalleryViewModelTest {
    
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    private val testDispatcher = StandardTestDispatcher()
    
    private lateinit var mockGetBatteryStylesUseCase: GetBatteryStylesUseCase
    private lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider
    private lateinit var viewModel: BatteryGalleryViewModel
    
    private val testStyles = listOf(
        BatteryStyle(
            id = "test1",
            name = "Heart Style",
            category = BatteryStyleCategory.HEART,
            batteryImageUrl = "https://example.com/battery1.png",
            emojiImageUrl = "https://example.com/emoji1.png",
            isPremium = false,
            isPopular = true
        ),
        BatteryStyle(
            id = "test2",
            name = "Cute Cat",
            category = BatteryStyleCategory.CUTE,
            batteryImageUrl = "https://example.com/battery2.png",
            emojiImageUrl = "https://example.com/emoji2.png",
            isPremium = true,
            isPopular = false
        ),
        BatteryStyle(
            id = "test3",
            name = "Premium Heart",
            category = BatteryStyleCategory.HEART,
            batteryImageUrl = "https://example.com/battery3.png",
            emojiImageUrl = "https://example.com/emoji3.png",
            isPremium = true,
            isPopular = true
        )
    )
    
    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)
        
        mockGetBatteryStylesUseCase = mockk()
        mockCoreBatteryStatsProvider = mockk()
        
        // Setup default mock behaviors
        every { mockGetBatteryStylesUseCase.getAllStylesFlow() } returns MutableStateFlow(testStyles)
        every { mockGetBatteryStylesUseCase.getLoadingStateFlow() } returns MutableStateFlow(false)
        every { mockCoreBatteryStatsProvider.coreBatteryStatusFlow } returns MutableStateFlow(
            CoreBatteryStatus.createDefault().copy(percentage = 85, isCharging = false)
        )
        every { mockGetBatteryStylesUseCase.hasCachedData() } returns true
        every { mockGetBatteryStylesUseCase.getCurrentStyles() } returns testStyles
        
        viewModel = BatteryGalleryViewModel(mockGetBatteryStylesUseCase, mockCoreBatteryStatsProvider)
    }
    
    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }
    
    @Test
    fun `initial state is correct`() = runTest {
        // When
        advanceUntilIdle()
        val state = viewModel.uiState.value
        
        // Then
        assertTrue(state.allStyles.isNotEmpty())
        assertEquals(testStyles.size, state.allStyles.size)
        assertFalse(state.isLoading)
        assertNull(state.errorMessage)
    }
    
    @Test
    fun `LoadInitialData event loads styles`() = runTest {
        // Given
        coEvery { mockGetBatteryStylesUseCase.getAllStyles(any()) } returns testStyles
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.LoadInitialData)
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertEquals(testStyles, state.allStyles)
        assertFalse(state.isLoading)
    }
    
    @Test
    fun `RefreshData event triggers refresh`() = runTest {
        // Given
        coEvery { mockGetBatteryStylesUseCase.refreshStyles() } returns true
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.RefreshData)
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertFalse(state.isRefreshing)
        assertTrue(state.lastRefreshTimestamp > 0)
    }
    
    @Test
    fun `FilterByCategory event filters styles correctly`() = runTest {
        // Given
        advanceUntilIdle() // Let initial state settle
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.FilterByCategory(BatteryStyleCategory.HEART))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertEquals(BatteryStyleCategory.HEART, state.selectedCategory)
        assertTrue(state.displayedStyles.all { it.category == BatteryStyleCategory.HEART })
    }
    
    @Test
    fun `ToggleShowOnlyFree event filters free styles`() = runTest {
        // Given
        advanceUntilIdle() // Let initial state settle
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.ToggleShowOnlyFree(true))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertTrue(state.showOnlyFree)
        assertFalse(state.showOnlyPremium)
        assertTrue(state.displayedStyles.all { !it.isPremium })
    }
    
    @Test
    fun `ToggleShowOnlyPremium event filters premium styles`() = runTest {
        // Given
        advanceUntilIdle() // Let initial state settle
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.ToggleShowOnlyPremium(true))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertTrue(state.showOnlyPremium)
        assertFalse(state.showOnlyFree)
        assertTrue(state.displayedStyles.all { it.isPremium })
    }
    
    @Test
    fun `ToggleShowOnlyPopular event filters popular styles`() = runTest {
        // Given
        advanceUntilIdle() // Let initial state settle
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.ToggleShowOnlyPopular(true))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertTrue(state.showOnlyPopular)
        assertTrue(state.displayedStyles.all { it.isPopular })
    }
    
    @Test
    fun `SearchStyles event filters by search query`() = runTest {
        // Given
        advanceUntilIdle() // Let initial state settle
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.SearchStyles("Heart"))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertEquals("Heart", state.searchQuery)
        assertTrue(state.displayedStyles.all { it.name.contains("Heart", ignoreCase = true) })
    }
    
    @Test
    fun `ClearAllFilters event resets all filters`() = runTest {
        // Given
        advanceUntilIdle()
        viewModel.handleEvent(BatteryGalleryEvent.FilterByCategory(BatteryStyleCategory.HEART))
        viewModel.handleEvent(BatteryGalleryEvent.ToggleShowOnlyFree(true))
        viewModel.handleEvent(BatteryGalleryEvent.SearchStyles("test"))
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.ClearAllFilters)
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertNull(state.selectedCategory)
        assertFalse(state.showOnlyFree)
        assertFalse(state.showOnlyPremium)
        assertFalse(state.showOnlyPopular)
        assertEquals("", state.searchQuery)
        assertEquals(testStyles.size, state.displayedStyles.size)
    }
    
    @Test
    fun `SelectStyle event updates selected style`() = runTest {
        // Given
        val targetStyle = testStyles.first()
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.SelectStyle(targetStyle))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertEquals(targetStyle.id, state.selectedStyleId)
    }
    
    @Test
    fun `DismissError event clears error message`() = runTest {
        // Given
        coEvery { mockGetBatteryStylesUseCase.refreshStyles() } returns false
        viewModel.handleEvent(BatteryGalleryEvent.RefreshData)
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.DismissError)
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertNull(state.errorMessage)
    }
    
    @Test
    fun `battery state changes are handled correctly`() = runTest {
        // Given
        val batteryStatusFlow = MutableStateFlow(
            CoreBatteryStatus.createDefault().copy(percentage = 50, isCharging = false)
        )
        every { mockCoreBatteryStatsProvider.coreBatteryStatusFlow } returns batteryStatusFlow
        
        // When
        batteryStatusFlow.value = CoreBatteryStatus.createDefault().copy(percentage = 75, isCharging = true)
        advanceUntilIdle()
        
        // Then - ViewModel should handle the battery state change
        // (Implementation depends on what the ViewModel does with battery state changes)
        val state = viewModel.uiState.value
        assertNotNull(state) // Basic assertion that state is updated
    }
    
    @Test
    fun `error handling works correctly`() = runTest {
        // Given
        coEvery { mockGetBatteryStylesUseCase.refreshStyles() } throws RuntimeException("Test error")
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.RefreshData)
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertFalse(state.isRefreshing)
        // Error should be handled gracefully without crashing
    }
    
    @Test
    fun `multiple filter combinations work correctly`() = runTest {
        // Given
        advanceUntilIdle()
        
        // When - Apply multiple filters
        viewModel.handleEvent(BatteryGalleryEvent.FilterByCategory(BatteryStyleCategory.HEART))
        viewModel.handleEvent(BatteryGalleryEvent.ToggleShowOnlyPopular(true))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.value
        assertTrue(state.displayedStyles.all { 
            it.category == BatteryStyleCategory.HEART && it.isPopular 
        })
    }
}
