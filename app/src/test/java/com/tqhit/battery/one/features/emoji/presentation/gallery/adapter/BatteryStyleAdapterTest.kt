package com.tqhit.battery.one.features.emoji.presentation.gallery.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.test.core.app.ApplicationProvider
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * Unit tests for BatteryStyleAdapter.
 * 
 * Tests the adapter layer following the established testing patterns:
 * - Uses Robolectric for Android component testing
 * - Tests adapter behavior and ViewHolder binding
 * - Verifies click listeners and premium indicators
 * - Tests DiffUtil callback functionality
 * - Includes error handling for image loading
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class BatteryStyleAdapterTest {
    
    private lateinit var context: Context
    private lateinit var adapter: BatteryStyleAdapter
    private lateinit var mockOnStyleClick: (BatteryStyle) -> Unit
    private lateinit var mockOnStyleLongClick: (BatteryStyle) -> Unit
    private lateinit var mockOnPremiumUnlockClick: (BatteryStyle) -> Unit
    private lateinit var mockOnImageLoadError: (String, String) -> Unit
    
    private val testStyles = listOf(
        BatteryStyle(
            id = "test1",
            name = "Heart Style",
            category = BatteryStyleCategory.HEART,
            batteryImageUrl = "https://example.com/battery1.png",
            emojiImageUrl = "https://example.com/emoji1.png",
            isPremium = false,
            isPopular = true
        ),
        BatteryStyle(
            id = "test2",
            name = "Cute Cat",
            category = BatteryStyleCategory.CUTE,
            batteryImageUrl = "https://example.com/battery2.png",
            emojiImageUrl = "https://example.com/emoji2.png",
            isPremium = true,
            isPopular = false
        ),
        BatteryStyle(
            id = "test3",
            name = "Premium Heart",
            category = BatteryStyleCategory.HEART,
            batteryImageUrl = "https://example.com/battery3.png",
            emojiImageUrl = "https://example.com/emoji3.png",
            isPremium = true,
            isPopular = true
        )
    )
    
    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        
        mockOnStyleClick = mockk(relaxed = true)
        mockOnStyleLongClick = mockk(relaxed = true)
        mockOnPremiumUnlockClick = mockk(relaxed = true)
        mockOnImageLoadError = mockk(relaxed = true)
        
        adapter = BatteryStyleAdapter(
            onStyleClick = mockOnStyleClick,
            onStyleLongClick = mockOnStyleLongClick,
            onPremiumUnlockClick = mockOnPremiumUnlockClick,
            onImageLoadError = mockOnImageLoadError
        )
    }
    
    @Test
    fun `adapter creates correct number of view holders`() {
        // Given
        adapter.submitList(testStyles)
        
        // When
        val itemCount = adapter.itemCount
        
        // Then
        assertEquals(testStyles.size, itemCount)
    }
    
    @Test
    fun `adapter returns correct item at position`() {
        // Given
        adapter.submitList(testStyles)
        
        // When
        val item = adapter.getItem(0)
        
        // Then
        assertEquals(testStyles[0], item)
    }
    
    @Test
    fun `view holder binds style data correctly`() {
        // Given
        adapter.submitList(testStyles)
        val parent = RecyclerView(context)
        
        // When
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // Then
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        assertEquals(testStyles[0].name, binding.styleNameText.text.toString())
        assertTrue(binding.categoryText.text.toString().contains(testStyles[0].category.displayName))
    }
    
    @Test
    fun `premium badge is visible for premium styles`() {
        // Given
        val premiumStyle = testStyles.find { it.isPremium }!!
        adapter.submitList(listOf(premiumStyle))
        val parent = RecyclerView(context)
        
        // When
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // Then
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        assertEquals(View.VISIBLE, binding.premiumBadge.visibility)
    }
    
    @Test
    fun `premium badge is hidden for free styles`() {
        // Given
        val freeStyle = testStyles.find { !it.isPremium }!!
        adapter.submitList(listOf(freeStyle))
        val parent = RecyclerView(context)
        
        // When
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // Then
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        assertEquals(View.GONE, binding.premiumBadge.visibility)
    }
    
    @Test
    fun `popular badge is visible for popular styles`() {
        // Given
        val popularStyle = testStyles.find { it.isPopular }!!
        adapter.submitList(listOf(popularStyle))
        val parent = RecyclerView(context)
        
        // When
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // Then
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        assertEquals(View.VISIBLE, binding.popularBadge.visibility)
    }
    
    @Test
    fun `popular badge is hidden for non-popular styles`() {
        // Given
        val nonPopularStyle = testStyles.find { !it.isPopular }!!
        adapter.submitList(listOf(nonPopularStyle))
        val parent = RecyclerView(context)
        
        // When
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // Then
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        assertEquals(View.GONE, binding.popularBadge.visibility)
    }
    
    @Test
    fun `action button is visible for premium styles`() {
        // Given
        val premiumStyle = testStyles.find { it.isPremium }!!
        adapter.submitList(listOf(premiumStyle))
        val parent = RecyclerView(context)
        
        // When
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // Then
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        assertEquals(View.VISIBLE, binding.actionButton.visibility)
        assertEquals("Unlock", binding.actionButton.text.toString())
    }
    
    @Test
    fun `action button is hidden for free styles`() {
        // Given
        val freeStyle = testStyles.find { !it.isPremium }!!
        adapter.submitList(listOf(freeStyle))
        val parent = RecyclerView(context)
        
        // When
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // Then
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        assertEquals(View.GONE, binding.actionButton.visibility)
    }
    
    @Test
    fun `style click triggers callback`() {
        // Given
        val targetStyle = testStyles[0]
        adapter.submitList(listOf(targetStyle))
        val parent = RecyclerView(context)
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // When
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        binding.styleCard.performClick()
        
        // Then
        verify { mockOnStyleClick(targetStyle) }
    }
    
    @Test
    fun `style long click triggers callback`() {
        // Given
        val targetStyle = testStyles[0]
        adapter.submitList(listOf(targetStyle))
        val parent = RecyclerView(context)
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // When
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        binding.styleCard.performLongClick()
        
        // Then
        verify { mockOnStyleLongClick(targetStyle) }
    }
    
    @Test
    fun `premium unlock click triggers callback`() {
        // Given
        val premiumStyle = testStyles.find { it.isPremium }!!
        adapter.submitList(listOf(premiumStyle))
        val parent = RecyclerView(context)
        val viewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(viewHolder, 0)
        
        // When
        val binding = (viewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        binding.actionButton.performClick()
        
        // Then
        verify { mockOnPremiumUnlockClick(premiumStyle) }
    }
    
    @Test
    fun `category text includes premium status`() {
        // Given
        val premiumStyle = testStyles.find { it.isPremium }!!
        val freeStyle = testStyles.find { !it.isPremium }!!
        
        adapter.submitList(listOf(premiumStyle))
        val parent = RecyclerView(context)
        
        // When - Test premium style
        val premiumViewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(premiumViewHolder, 0)
        val premiumBinding = (premiumViewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        
        // Then
        assertTrue(premiumBinding.categoryText.text.toString().contains("Premium"))
        
        // When - Test free style
        adapter.submitList(listOf(freeStyle))
        val freeViewHolder = adapter.onCreateViewHolder(parent, 0)
        adapter.onBindViewHolder(freeViewHolder, 0)
        val freeBinding = (freeViewHolder as BatteryStyleAdapter.BatteryStyleViewHolder).binding
        
        // Then
        assertTrue(freeBinding.categoryText.text.toString().contains("Free"))
    }
    
    @Test
    fun `adapter handles list updates correctly`() {
        // Given
        val initialList = listOf(testStyles[0])
        val updatedList = listOf(testStyles[0], testStyles[1])

        // When
        adapter.submitList(initialList)
        assertEquals(1, adapter.itemCount)

        adapter.submitList(updatedList)

        // Then
        assertEquals(2, adapter.itemCount)
    }

    @Test
    fun `adapter handles empty list correctly`() {
        // Given
        adapter.submitList(testStyles)
        assertEquals(testStyles.size, adapter.itemCount)

        // When
        adapter.submitList(emptyList())

        // Then
        assertEquals(0, adapter.itemCount)
    }
}
